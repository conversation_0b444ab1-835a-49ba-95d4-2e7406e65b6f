import * as crypto from "crypto";
import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import * as moment from "moment";

const logger = functions.logger;

const app = admin.initializeApp();
const firestore = app.firestore();

/**
* Generate randomized timestamp ID
* @return {string}
*/
function generateID() {
    const _date = moment().format("YYMMDDhhmmss");
    const _randomNumber = (Math.floor(Math.random() * (9999999 - 1000000 + 1)) + 1000000).toString();
    return `${_date}${_randomNumber}`;
}

export const processListingRaspberryPiLogs = functions.region("asia-southeast1")
    .https.onRequest(async (request, response) => {
        // 1. Retrieve authorization bearer token from http request header.
        logger.info("👶 1 of 6: Retrieve authorization bearer token from http request header.");
        const authString = request.get("Authorization") ?? "";
        const authStringArray = authString.split("Bearer ");

        let isSuccess = false;
        let apiKeyHashed = "";

        if (authStringArray.length > 1) {
            apiKeyHashed = authStringArray[1];
        }

        // 2. Retrieve pi event data from http request body.
        logger.info("👶 2 of 6: Retrieve pi event data from http request body.");
        const data = request.body;
        const listingId = data.listing_id;
        const timestamp = data.timestamp;
        const isDisinfecting = data.is_disinfecting;
        const isDoorOpened = data.is_door_opened;
        const isOccupied = data.is_occupied;
        const isLedLightOn = data.is_led_light_on;
        const isFanOn = data.is_fan_on;
        const isScheduled = data.is_scheduled ?? false;
        const isUVCLampOn = data.is_uvc_lamp_on;
        const temperature = data.temperature;
        const humidity = data.humidity;

        // 3. Get the firestore collection snapshot.
        logger.info("👶 3 of 6: Get the firestore collection snapshot.");
        const batch = firestore.batch();
        const listingsCollectionName = "listings";
        const listingLogsCollectionName = "listing_logs";
        const listingLogId = generateID();
        const listingsEndpoint = firestore.collection(listingsCollectionName).doc(listingId);
        const listingLogsEndpoint = firestore.collection(listingLogsCollectionName).doc(listingLogId);
        const listingsSnapshot = await listingsEndpoint.get();
        const doc = listingsSnapshot.data() ?? {};
        const apiKey = doc.api_key;
        const piId = doc.pi_id;
        const pwd = `${apiKey}${piId}${timestamp}`;
        const generatedHash = crypto.createHash("sha256").update(pwd).digest("hex").toLowerCase();
        const currentTimestamp = new Date(timestamp * 1000);

        if (apiKeyHashed === generatedHash) {
            // 4. Update listing document in firestore.
            logger.info("👶 4 of 6: Update listing document in firestore.");
            isSuccess = true;
            batch.update(listingsEndpoint, {
                modified_at: currentTimestamp,
                pi_last_updated_at: currentTimestamp,
                is_disinfecting: isDisinfecting,
                is_door_opened: isDoorOpened,
                is_occupied: isOccupied,
                is_led_light_on: isLedLightOn,
                is_fan_on: isFanOn,
                is_uvc_lamp_on: isUVCLampOn,
                temperature: temperature,
                humidity: humidity,
            });

            // 4. Create listing log document in firestore.
            logger.info("👶 5 of 6: Create listing log document in firestore.");
            batch.create(listingLogsEndpoint, {
                id: listingLogId,
                created_at: currentTimestamp,
                modified_at: currentTimestamp,
                is_disinfecting: isDisinfecting,
                is_door_opened: isDoorOpened,
                is_occupied: isOccupied,
                is_led_light_on: isLedLightOn,
                is_fan_on: isFanOn,
                is_scheduled: isScheduled,
                is_uvc_lamp_on: isUVCLampOn,
                temperature: temperature,
                humidity: humidity,
                listing_id: listingId,
                listing: {
                    id: listingId,
                },
            });

            batch.commit();
        }

        // 6. Return the json response with success status.
        logger.info(`👶 6 of 6: Return the json response with success status: ${isSuccess}`);
        response.send({
            is_success: isSuccess,
        });
    });
