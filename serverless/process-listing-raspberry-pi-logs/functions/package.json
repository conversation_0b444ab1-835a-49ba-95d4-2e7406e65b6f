{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "serve": "npm run build && firebase emulators:start --only functions:processListingRaspberryPiLogs", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "config-dev": "firebase functions:config:set project.id=gomama-dev", "config-prod": "firebase functions:config:set project.id=gomama-prod", "deploy-dev": "firebase use gomama-dev && npm run config-dev && firebase deploy --only functions:processListingRaspberryPiLogs", "deploy-prod": "firebase use gomama-prod && npm run config-prod && firebase deploy --only functions:processListingRaspberryPiLogs", "logs": "firebase functions:log"}, "engines": {"node": "14"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^9.8.0", "firebase-functions": "^3.16.0", "moment": "^2.29.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^3.9.1", "@typescript-eslint/parser": "^3.8.0", "eslint": "^7.6.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.22.0", "firebase-functions-test": "^0.2.0", "typescript": "^3.8.0"}, "private": true}