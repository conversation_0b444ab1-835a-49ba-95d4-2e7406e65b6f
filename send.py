#!/usr/bin/env python3
# -*- coding:utf-8 -*-

import json
import logging
import time

import coloredlogs
import requests

from helper import *

api_key = ''
pod_id = ''
pi_id = ''
url = ''
timestamp = ''
loop_timestamp = time.time()
listing_data = {}
listing_id = ''

is_send_data = False
is_occupied = False
is_disinfecting = False
is_occupied_status_changed = False
start_disinfecting = False
is_send_data = False
is_scheduled = False
is_led_light_on = False
is_fan_on = False
is_uvc_lamp_on = False
is_door_opened = False

temperature = 0
humidity = 0

logger = logging.getLogger(__name__)
coloredlogs.install(level=logging.DEBUG, logger=logger,
                    fmt='%(name)s - %(levelname)s - %(message)s')
logging.getLogger().setLevel(logging.INFO)
logging.getLogger().setLevel(logging.WARNING)
logging.getLogger().setLevel(logging.ERROR)

# Initialise config


def init_config():
    global api_key, apn, pod_id, pi_id, usb_port, baud_rate, url, timestamp
    with open('/home/<USER>/Desktop/gomama-raspberrypi/config.json') as f:
        try:
            data = json.load(f)
            if 'api_key' in data:
                api_key = data['api_key']
            if 'pod_id' in data:
                pod_id = data['pod_id']
            if 'pi_id' in data:
                pi_id = data['pi_id']
            else:
                write_pi_config()
            if 'url' in data:
                url = data['url']
            timestamp = get_current_timestamp()
            print(data)
        except json.decoder.JSONDecodeError as err:
            logger.error("JSON Decode Error", err)
            pass


def init_data():
    global listing_data, listing_id, timestamp, is_disinfecting, is_door_opened, is_occupied, is_led_light_on, is_fan_on, is_scheduled, is_uvc_lamp_on, temperature, humidity, is_send_data
    with open('/home/<USER>/Desktop/gomama-raspberrypi/data.json') as f:
        try:
            data = json.load(f)
            # listing_data = 1013995107100112091
            # listing_data = data
            # if '1013995107100112091' in data:
                # listing_id = data['listing_id']
            if 'timestamp' in data:
                timestamp = data['timestamp']
            if 'is_disinfecting' in data:
                is_disinfecting = data['is_disinfecting']
            if 'is_door_opened' in data:
                is_door_opened = data['is_door_opened']
            if 'is_occupied' in data:
                is_occupied = data['is_occupied']
            if 'is_led_light_on' in data:
                is_led_light_on = data['is_led_light_on']
            if 'is_fan_on' in data:
                is_fan_on = data['is_fan_on']
            if 'is_scheduled' in data:
                is_scheduled = data['is_scheduled']
            if 'is_uvc_lamp_on' in data:
                is_uvc_lamp_on = data['is_uvc_lamp_on']
            if 'temperature' in data:
                temperature = data['temperature']
            if 'humidity' in data:
                humidity = data['humidity']
            if 'is_send_data' in data:
                is_send_data = data['is_send_data']
        except json.decoder.JSONDecodeError as err:
            logger.error("JSON Decode Error", err)
            if listing_data:
                write_data(listing_data)


def post_https(pi_key_hashed):
    https_headers = {'Authorization': f'Bearer {pi_key_hashed}',
                     'Content-Type': 'application/json'}
    data = json.dumps(listing_data)
    try:
        response = requests.post(url=url, data=data, headers=https_headers)
        logger.warning(f'{response}')
        logger.warning(f'* [E3372] server response: {response.text}')
    except requests.exceptions.RequestException as err:
        logger.error("Request Exception:", err)
        pass
    except requests.exceptions.HTTPError as errh:
        logger.error("Http Error:", errh)
        pass
    except requests.exceptions.ConnectionError as errc:
        logger.error("Connection Error:", errc)
        pass
    except requests.exceptions.Timeout as errt:
        logger.error("Timeout Error:", errt)
        pass
    except:
        logger.error("Error")
        pass


def update_and_send_data():
    global is_scheduled, is_send_data
    logger.warning('* [E3372] sending data...')
    init_data()
    listing_data['listing_id'] = listing_id = '1013995107100112091'
    listing_data['timestamp'] = loop_timestamp
    listing_data['is_disinfecting'] = is_disinfecting
    listing_data['is_door_opened'] = is_door_opened
    listing_data['is_occupied'] = is_occupied
    listing_data['is_led_light_on'] = is_led_light_on
    listing_data['is_fan_on'] = is_fan_on
    listing_data['is_scheduled'] = is_scheduled
    listing_data['is_uvc_lamp_on'] = is_uvc_lamp_on
    listing_data['temperature'] = temperature
    listing_data['humidity'] = humidity
    listing_data['is_send_data'] = False
    logger.warning(f'* [E3372] listing data: {listing_data}')
    print(api_key, pi_id, loop_timestamp)
    pi_key_hashed = generate_api_key_hashed(
        api_key, pi_id, loop_timestamp)
    logger.warning(f'{pi_key_hashed}')
    post_https(pi_key_hashed)


def start_send_module():
    global is_send_data, loop_timestamp
    init_config()
    loop_timestamp = time.time()
    while 1:
        time.sleep(0.2)    #ori = 0.05, maybe change to 0.005
        if time.time() >= loop_timestamp + 1:
            logger.info(
                f'[LOOP] start send data status cycle at {loop_timestamp}...')
            update_and_send_data()
            loop_timestamp = time.time()
            logger.info(
                f'[LOOP] end send data status cycle at {loop_timestamp}...')
            print('\n==========================================================\n')
        time.sleep(0.2)    #ori = 0.05


start_send_module()
